archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.6.0/lib/
async
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/
bloc
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/
characters
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
cli_util
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib/
clipboard
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clipboard-0.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clipboard-0.1.3/lib/
clock
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
connectivity_plus
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/lib/
connectivity_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dbus
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/
device_frame
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib/
device_info_plus
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/
device_info_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib/
device_preview
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib/
equatable
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/
excel
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6/lib/
ffi
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
flutter_bloc
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/
flutter_breadcrumb
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/lib/
flutter_launcher_icons
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/lib/
flutter_plugin_android_lifecycle
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.24/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.24/lib/
flutter_screenutil
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/
flutter_secure_storage
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.3/lib/
flutter_secure_storage_linux
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.2/lib/
flutter_secure_storage_macos
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib/
flutter_secure_storage_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/
flutter_secure_storage_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib/
flutter_secure_storage_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/
flutter_svg
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.17/lib/
flutter_switch
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_switch-0.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_switch-0.3.2/lib/
freezed_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib/
google_fonts
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/
http
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/
http_parser
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/
image
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0/lib/
in_app_update
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/in_app_update-4.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/in_app_update-4.2.3/lib/
intl
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/
js
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
loading_animation_widget
2.16
file:///Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib/
local_auth
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/lib/
local_auth_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.46/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.46/lib/
local_auth_darwin
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.2/lib/
local_auth_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/
local_auth_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/
open_file
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file-3.5.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file-3.5.10/lib/
open_file_android
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/lib/
open_file_ios
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/lib/
open_file_linux
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/lib/
open_file_mac
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/lib/
open_file_platform_interface
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/lib/
open_file_web
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_web-0.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_web-0.0.4/lib/
open_file_windows
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/lib/
path
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
path_parsing
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/
path_provider
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/lib/
path_provider_foundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
pedantic
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pedantic-1.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pedantic-1.11.1/lib/
permission_handler
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.3.1/lib/
permission_handler_android
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.0.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.0.13/lib/
permission_handler_apple
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/lib/
permission_handler_html
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib/
permission_handler_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/
pin_code_fields
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/
pin_input_text_field
2.13
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/
responsive_framework
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-1.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-1.5.1/lib/
screenshot
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/screenshot-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/screenshot-3.0.0/lib/
shared_preferences
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.1/lib/
shared_preferences_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
sms_autofill
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/lib/
source_span
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0/lib/
super_tooltip
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/super_tooltip-2.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/super_tooltip-2.0.9/lib/
syncfusion_flutter_charts
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib/
syncfusion_flutter_core
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib/
term_glyph
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/
timeago
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/
url_launcher_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/lib/
url_launcher_ios
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/lib/
url_launcher_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/lib/
url_launcher_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.3/lib/
vector_graphics
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.15/lib/
vector_graphics_codec
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/
vector_graphics_compiler
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
vibration
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vibration-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vibration-2.0.1/lib/
vibration_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vibration_platform_interface-0.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vibration_platform_interface-0.0.2/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.0/lib/
win32
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/lib/
win32_registry
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
sky_engine
3.7
file:///Users/<USER>/Development/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/Development/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///Users/<USER>/Development/flutter/packages/flutter/
file:///Users/<USER>/Development/flutter/packages/flutter/lib/
flutter_localizations
3.7
file:///Users/<USER>/Development/flutter/packages/flutter_localizations/
file:///Users/<USER>/Development/flutter/packages/flutter_localizations/lib/
flutter_web_plugins
3.7
file:///Users/<USER>/Development/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/Development/flutter/packages/flutter_web_plugins/lib/
water_metering
3.3
file:///Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/
file:///Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/lib/
country_code_picker
2.12
file:///Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/
file:///Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/country_code_picker/lib/
dropdown_button2
2.12
file:///Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/dropdown_button2/
file:///Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/local_packages/dropdown_button2/lib/
2
